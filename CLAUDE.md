# DungeonRPG Project Information

## Overview
DungeonRPG is a browser-based retro-style dungeon crawler RPG game built with vanilla JavaScript, HTML5, and CSS.

## Project Structure
```
DungeonRPG/
├── index.html          # Main game entry point
├── style.css           # Game styling
├── js/                 # JavaScript game code
│   ├── main.js         # Game initialization
│   ├── core/           # Core game systems
│   │   ├── Constants.js
│   │   ├── GameCore.js
│   │   └── GameState.js
│   ├── systems/        # Game mechanics
│   │   ├── Character.js
│   │   ├── Combat.js
│   │   ├── Dungeon.js
│   │   ├── Player.js
│   │   ├── Renderer.js
│   │   └── SoundManager.js
│   ├── ui/             # User interface components
│   │   ├── UIManager.js
│   │   ├── InputHandler.js
│   │   ├── TitleScreen.js
│   │   ├── OptionScreen.js
│   │   └── [Various notification screens]
│   └── utils/          # Utility modules
│       └── ImageManager.js
├── assets/             # Game assets
│   ├── audio/          # Sound effects and music
│   └── images/         # Sprites and graphics
└── docs/               # Documentation

## Key Features
- Turn-based combat system
- Multiple enemy types with different abilities
- Item and skill system (Orbs, potions, etc.)
- Dungeon exploration with multiple floors
- Sound effects and background music
- Save/load game functionality

## Running the Game
1. Open `index.html` in a modern web browser
2. No build process or dependencies required
3. Game runs entirely in the browser

## Development Notes
- Uses vanilla JavaScript (no frameworks)
- Modular architecture with clear separation of concerns
- Image and sound assets are preloaded via managers
- Game state is persisted in localStorage

## Recent Updates
- Added CARRIER enemy configuration
- Equipment skill system additions
- Orb and skill system adjustments

### Audio System
- The game already has a working audio system for combat sounds and background music
- Sound effects should be triggered at the exact moment the corresponding game event occurs
- Maintain the existing audio system's browser compatibility and fallback mechanisms

### Code Quality 
   - Maintain all existing file dependencies and game functionality
   - Use minimal code changes by leveraging the existing game mechanisms

### Testing and Cleanup
- If any temporary test files are created during development, delete them after task completion
- Verify that the newly created codes don't conflict with existing systems

### Monster Implementation Requirements
- Integrate the monster definition into the existing Constants.js file following the same structure and naming conventions as other monsters
- Ensure the monster follows established patterns for the existing monster types
- Make minimal code changes while preserving all existing functionality
- Maintain compatibility with the Renderer.js system and combat mechanics
- Verify the monster integrates properly with the existing spawn system and floor-based encounter mechanics