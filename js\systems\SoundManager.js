// ===== SOUND MANAGER =====
// Audio effects system for the game using Web Audio API

class SoundManager {
    constructor() {
        this.audioContext = null;
        this.masterVolume = 0.6;
        this.enabled = true;
        this.backgroundMusic = null;
        this.backgroundMusicGain = null;
        this.backgroundMusicVolume = 0.8; // Increased from 0.2 to 0.6 for better title screen music volume
        this.audioContextInitialized = false;
        this.userInteractionReceived = false;

        // Combat music system
        this.combatMusic = null;
        this.combatMusicVolume = 0.8; // Volume for combat and boss battle music
        this.previousMusic = null; // Store previous music state for restoration
        this.previousMusicPath = null;
        this.previousMusicCurrentTime = 0;
        this.inCombatMode = false;
        this.debug = true; // Set to true to enable debug logging

        // Floor-based music system
        this.floorChangeListenerAdded = false;

        // Don't initialize audio context immediately - wait for user interaction
    }
    
    // Initialize Web Audio API context (called after user interaction)
    initializeAudioContext() {
        if (this.audioContextInitialized) {
            return true;
        }

        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.audioContextInitialized = true;
            console.log('Audio context initialized successfully');
            return true;
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
            this.enabled = false;
            return false;
        }
    }

    // Initialize audio context after user interaction (required for modern browsers)
    async initializeAfterUserInteraction() {
        if (this.audioContextInitialized) {
            return { success: true };
        }

        try {
            // Mark that user interaction has been received
            this.userInteractionReceived = true;

            // Initialize audio context
            const success = this.initializeAudioContext();
            if (!success) {
                return { success: false, error: 'Failed to initialize audio context' };
            }

            // Resume if suspended
            await this.resumeAudioContext();

            console.log('Audio system initialized after user interaction');
            return { success: true };
        } catch (error) {
            console.error('Failed to initialize audio after user interaction:', error);
            return { success: false, error: error.message };
        }
    }
    
    // Resume audio context if suspended (required for user interaction)
    async resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
    }
    
    // Create a basic oscillator node
    createOscillator(frequency, type = 'sine') {
        if (!this.enabled || !this.audioContext) return null;
        
        const oscillator = this.audioContext.createOscillator();
        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        oscillator.type = type;
        return oscillator;
    }
    
    // Create a gain node for volume control
    createGain(volume = 1) {
        if (!this.enabled || !this.audioContext) return null;
        
        const gainNode = this.audioContext.createGain();
        gainNode.gain.setValueAtTime(volume * this.masterVolume, this.audioContext.currentTime);
        return gainNode;
    }
    
    // Create noise buffer for percussive sounds
    createNoiseBuffer(duration = 0.1) {
        if (!this.enabled || !this.audioContext) return null;
        
        const bufferSize = this.audioContext.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = buffer.getChannelData(0);
        
        for (let i = 0; i < bufferSize; i++) {
            output[i] = Math.random() * 2 - 1;
        }
        
        return buffer;
    }
    
    // Legacy method for backward compatibility - now uses new system
    playAttackHit() {
        this.playCombatSound('player_physical_attack');
    }
    
    // Legacy method for backward compatibility - now uses new system
    playMagicHit() {
        this.playCombatSound('magic_attack');
    }
    
    // Play defend sound effect
    playDefend() {
        this.resumeAudioContext().then(() => {
            if (!this.enabled || !this.audioContext) return;
            
            const now = this.audioContext.currentTime;
            
            // Create shield/guard sound
            const osc = this.createOscillator(150, 'square');
            const gain = this.createGain(0.3);
            const filter = this.audioContext.createBiquadFilter();
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(300, now);
            
            // Quick attack and decay
            gain.gain.setValueAtTime(0, now);
            gain.gain.linearRampToValueAtTime(0.3 * this.masterVolume, now + 0.01);
            gain.gain.exponentialRampToValueAtTime(0.001, now + 0.08);
            
            osc.connect(filter);
            filter.connect(gain);
            gain.connect(this.audioContext.destination);
            
            osc.start(now);
            osc.stop(now + 0.08);
        });
    }
    
    // Play evade sound effect
    playEvade() {
        this.resumeAudioContext().then(() => {
            if (!this.enabled || !this.audioContext) return;
            
            const now = this.audioContext.currentTime;
            
            // Create whoosh sound
            const osc = this.createOscillator(200, 'sawtooth');
            const gain = this.createGain(0.2);
            const filter = this.audioContext.createBiquadFilter();
            filter.type = 'highpass';
            filter.frequency.setValueAtTime(400, now);
            filter.frequency.linearRampToValueAtTime(1200, now + 0.15);
            
            gain.gain.setValueAtTime(0, now);
            gain.gain.linearRampToValueAtTime(0.2 * this.masterVolume, now + 0.02);
            gain.gain.linearRampToValueAtTime(0, now + 0.15);
            
            osc.frequency.linearRampToValueAtTime(100, now + 0.15);
            
            osc.connect(filter);
            filter.connect(gain);
            gain.connect(this.audioContext.destination);
            
            osc.start(now);
            osc.stop(now + 0.15);
        });
    }
    
    // Legacy method for backward compatibility - now uses new system
    playEnemyDefeat() {
        this.playCombatSound('enemy_defeat');
    }
    
    // Play flee success sound
    playFleeSuccess() {
        this.resumeAudioContext().then(() => {
            if (!this.enabled || !this.audioContext) return;

            const now = this.audioContext.currentTime;

            // Quick ascending sound
            const osc = this.createOscillator(440, 'sine');
            const gain = this.createGain(0.2);

            osc.frequency.linearRampToValueAtTime(880, now + 0.1);

            gain.gain.setValueAtTime(0, now);
            gain.gain.linearRampToValueAtTime(0.2 * this.masterVolume, now + 0.02);
            gain.gain.exponentialRampToValueAtTime(0.001, now + 0.1);

            osc.connect(gain);
            gain.connect(this.audioContext.destination);

            osc.start(now);
            osc.stop(now + 0.1);
        });
    }

    // ===== NEW COMBAT SOUND SYSTEM =====

    // Play combat sound effect based on action type and character class
    playCombatSound(soundType, characterClass = null) {
        if (!this.enabled) return;

        let audioFile = null;

        switch(soundType) {
            case 'player_physical_attack':
                if (characterClass === 'WARRIOR' || characterClass === 'RANGER') {
                    audioFile = 'assets/audio/slash.mp3';
                } else if (characterClass === 'MAGE') {
                    audioFile = 'assets/audio/blow.mp3';
                } else {
                    audioFile = 'assets/audio/slash.mp3'; // Default
                }
                break;
            case 'monster_physical_attack':
                audioFile = 'assets/audio/damage.mp3';
                break;
            case 'magic_attack':
                audioFile = 'assets/audio/magic.mp3';
                break;
            case 'breath_attack':
                audioFile = 'assets/audio/breath.mp3';
                break;
            case 'poison_attack':
                audioFile = 'assets/audio/poisonattack.mp3';
                break;
            case 'first_aid':
                audioFile = 'assets/audio/recover.mp3';
                break;
            case 'enemy_defeat':
                audioFile = 'assets/audio/defeat.mp3';
                break;
            default:
                console.warn(`Unknown sound type: ${soundType}`);
                return;
        }

        this.playAudioFile(audioFile);
    }

    // Play audio file using HTML Audio element
    playAudioFile(audioPath) {
        if (!this.enabled) {
            console.log('SoundManager disabled, skipping audio playback');
            return;
        }

        try {
            const audio = new Audio(audioPath);
            audio.volume = this.masterVolume * 0.8; // Slightly lower volume for effects

            // Handle errors gracefully with enhanced logging
            audio.addEventListener('error', (e) => {
                console.warn(`Failed to play audio file: ${audioPath}`, {
                    error: e,
                    audioSrc: audio.src,
                    audioError: audio.error
                });
            });

            audio.addEventListener('loadstart', () => {
                if (this.debug) {
                    console.log(`Loading audio: ${audioPath}`);
                }
            });

            audio.addEventListener('canplaythrough', () => {
                if (this.debug) {
                    console.log(`Audio ready to play: ${audioPath}`);
                }
            });

            audio.play().catch(error => {
                console.warn(`Failed to play audio: ${audioPath}`, {
                    error: error.message,
                    name: error.name,
                    audioPath: audioPath,
                    audioReadyState: audio.readyState,
                    audioNetworkState: audio.networkState
                });
            });

            if (this.debug) {
                console.log(`Audio playback initiated: ${audioPath}`);
            }
        } catch (error) {
            console.warn(`Error creating audio element for: ${audioPath}`, {
                error: error.message,
                stack: error.stack,
                audioPath: audioPath
            });
        }
    }

    // Play button sound effect
    playButtonSound() {
        if (!this.enabled) return;
        this.playAudioFile('assets/audio/button.mp3');
    }

    // ===== NEW GAME EVENT SOUND EFFECTS =====

    // Play healing point usage sound effect
    playHealingPointSound() {
        if (!this.enabled) return;
        this.playAudioFile('assets/audio/healingpoint.mp3');
    }

    // Play door opening sound effect
    playDoorOpenSound() {
        if (!this.enabled) return;
        this.playAudioFile('assets/audio/opendoor.mp3');
    }

    // Play pitfall trap sound effect
    playPitfallSound() {
        if (!this.enabled) return;
        this.playAudioFile('assets/audio/pitfall.mp3');
    }

    // Play critical damage received sound effect
    playCriticalDamageSound() {
        if (!this.enabled) return;
        this.playAudioFile('assets/audio/critical.mp3');
    }

    // Set master volume
    setVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
    }

    // Enable/disable sound
    setEnabled(enabled) {
        this.enabled = enabled;
    }

    // Verify audio file exists (CORS-safe for local files)
    async verifyAudioFile(audioPath) {
        return new Promise((resolve) => {
            const audio = new Audio();

            const cleanup = () => {
                audio.removeEventListener('canplaythrough', onSuccess);
                audio.removeEventListener('error', onError);
                audio.removeEventListener('abort', onError);
            };

            const onSuccess = () => {
                cleanup();
                resolve({ success: true, status: 200 });
            };

            const onError = (error) => {
                cleanup();
                if (this.debug) {
                    console.warn(`Audio file verification failed for ${audioPath}:`, error);
                }
                resolve({ success: false, error: error.message || 'Audio file not accessible' });
            };

            audio.addEventListener('canplaythrough', onSuccess);
            audio.addEventListener('error', onError);
            audio.addEventListener('abort', onError);

            // Set source to trigger loading
            audio.src = audioPath;
            audio.load();
        });
    }

    // Get the appropriate background music file based on current floor
    getFloorBasedMusicPath(floorNumber = null) {
        // Use current floor from gameState if no floor number provided
        const currentFloor = floorNumber || (typeof gameState !== 'undefined' ? gameState.currentFloor : 1);

        // Floor-based music selection
        if (currentFloor >= 10) {
            return 'assets/audio/The Search.m4a';
        } else if (currentFloor >= 7) {
            return 'assets/audio/Profundo.m4a';
        } else if (currentFloor >= 4) {
            return 'assets/audio/Notturno.m4a';
        } else {
            return 'assets/audio/The Fall of Love.m4a';
        }
    }

    // Play floor-appropriate background music
    async playFloorBasedBackgroundMusic(floorNumber = null) {
        const musicPath = this.getFloorBasedMusicPath(floorNumber);
        console.log(`Playing floor-based background music for floor ${floorNumber || gameState.currentFloor}: ${musicPath}`);
        return await this.playBackgroundMusic(musicPath);
    }

    // Set up floor change event listener
    setupFloorChangeListener() {
        if (this.floorChangeListenerAdded || typeof gameState === 'undefined') {
            return;
        }

        gameState.addEventListener(CONSTANTS.EVENTS.FLOOR_CHANGED, (data) => {
            // Only change music if not in combat mode
            if (!this.inCombatMode) {
                console.log(`Floor changed to ${data.floor}, updating background music`);
                this.playFloorBasedBackgroundMusic(data.floor).then(result => {
                    if (result.success) {
                        console.log('Floor-based background music updated successfully');
                    } else {
                        console.warn('Failed to update floor-based background music:', result.error);
                    }
                }).catch(error => {
                    console.error('Error updating floor-based background music:', error);
                });
            } else {
                console.log('Floor changed but in combat mode, music change deferred');
            }
        });

        this.floorChangeListenerAdded = true;
        console.log('Floor change event listener set up');
    }

    // Load and play background music (CORS-safe for local files)
    async playBackgroundMusic(audioPath) {
        if (!this.enabled) {
            console.warn('Audio not enabled');
            return { success: false, error: 'Audio not enabled' };
        }

        try {
            // Verify audio file exists first
            const fileCheck = await this.verifyAudioFile(audioPath);
            if (!fileCheck.success) {
                throw new Error(`Audio file not found or inaccessible: ${audioPath}`);
            }

            // Stop any existing background music
            this.stopBackgroundMusic();

            // Create HTML5 Audio element for CORS-safe local file access
            console.log(`Loading background music: ${audioPath}`);
            this.backgroundMusic = new Audio(audioPath);

            // Configure audio element
            this.backgroundMusic.loop = true; // Enable looping
            this.backgroundMusic.volume = this.backgroundMusicVolume * this.masterVolume;

            // Set up event handlers
            return new Promise((resolve) => {
                const cleanup = () => {
                    this.backgroundMusic.removeEventListener('canplaythrough', onCanPlay);
                    this.backgroundMusic.removeEventListener('error', onError);
                };

                const onCanPlay = () => {
                    cleanup();
                    // Start playback
                    this.backgroundMusic.play()
                        .then(() => {
                            console.log('Background music started successfully');
                            resolve({ success: true });
                        })
                        .catch((error) => {
                            console.error('Failed to start background music playback:', error);
                            this.backgroundMusic = null;
                            resolve({ success: false, error: error.message });
                        });
                };

                const onError = (error) => {
                    cleanup();
                    if (this.debug) {
                        console.error('Failed to load background music:', error);
                    }
                    this.backgroundMusic = null;
                    resolve({ success: false, error: error.message || 'Audio loading failed' });
                };

                this.backgroundMusic.addEventListener('canplaythrough', onCanPlay);
                this.backgroundMusic.addEventListener('error', onError);

                // Start loading
                this.backgroundMusic.load();
            });

        } catch (error) {
            if (this.debug) {
                console.error('Failed to load/play background music:', error);
            }
            this.backgroundMusic = null;
            return { success: false, error: error.message };
        }
    }

    // Stop background music
    stopBackgroundMusic() {
        if (this.backgroundMusic) {
            try {
                this.backgroundMusic.pause();
                this.backgroundMusic.currentTime = 0;
                console.log('Background music stopped');
            } catch (error) {
                // Ignore errors when stopping (might already be stopped)
                console.log('Background music stop called (may already be stopped)');
            }
            this.backgroundMusic = null;
        }
    }

    // Set background music volume
    setBackgroundMusicVolume(volume) {
        this.backgroundMusicVolume = Math.max(0, Math.min(1, volume));
        if (this.backgroundMusic) {
            this.backgroundMusic.volume = this.backgroundMusicVolume * this.masterVolume;
        }
    }

    // Check if background music is playing
    isBackgroundMusicPlaying() {
        return this.backgroundMusic !== null && !this.backgroundMusic.paused;
    }

    // Start combat music and store previous music state
    async startCombatMusic() {
        if (!this.enabled) {
            console.warn('Audio not enabled');
            return { success: false, error: 'Audio not enabled' };
        }

        if (this.inCombatMode) {
            console.log('Combat music already active');
            return { success: true };
        }

        try {
            // Store current background music state for restoration
            if (this.backgroundMusic && !this.backgroundMusic.paused) {
                // Store the current playback time precisely
                this.previousMusicCurrentTime = this.backgroundMusic.currentTime;
                
                // Extract the clean file path from the src attribute
                let srcPath = this.backgroundMusic.src;
                if (srcPath.startsWith('file:///')) {
                    // Convert file:// URL to relative path
                    const url = new URL(srcPath);
                    this.previousMusicPath = url.pathname.split('/').pop();
                    this.previousMusicPath = 'assets/audio/' + this.previousMusicPath;
                } else if (srcPath.includes('assets/audio/')) {
                    // Extract the assets/audio/ part and filename
                    const assetIndex = srcPath.indexOf('assets/audio/');
                    this.previousMusicPath = srcPath.substring(assetIndex);
                } else {
                    // Fallback - use the current floor-based music path
                    this.previousMusicPath = this.getFloorBasedMusicPath();
                }

                console.log(`Storing previous music state - Path: ${this.previousMusicPath}, Time: ${this.previousMusicCurrentTime.toFixed(2)}s`);
            } else {
                // No music was playing, store null values
                this.previousMusicPath = null;
                this.previousMusicCurrentTime = 0;
                console.log('No background music was playing when combat started');
            }

            // Start combat music
            const combatMusicPath = 'assets/audio/Barbarian Attack.m4a';
            console.log('Starting combat music:', combatMusicPath);

            // Verify combat music file exists
            const fileCheck = await this.verifyAudioFile(combatMusicPath);
            if (!fileCheck.success) {
                throw new Error(`Combat music file not found: ${combatMusicPath}`);
            }

            // Stop current background music
            this.stopBackgroundMusic();

            // Create combat music element
            this.combatMusic = new Audio(combatMusicPath);
            this.combatMusic.loop = true;
            this.combatMusic.volume = this.backgroundMusicVolume * this.masterVolume;

            // Set up event handlers for combat music
            return new Promise((resolve) => {
                const cleanup = () => {
                    this.combatMusic.removeEventListener('canplaythrough', onCanPlay);
                    this.combatMusic.removeEventListener('error', onError);
                };

                const onCanPlay = () => {
                    cleanup();
                    this.combatMusic.play()
                        .then(() => {
                            this.inCombatMode = true;
                            console.log('Combat music started successfully');
                            resolve({ success: true });
                        })
                        .catch((error) => {
                            console.error('Failed to start combat music playback:', error);
                            this.combatMusic = null;
                            resolve({ success: false, error: error.message });
                        });
                };

                const onError = (error) => {
                    cleanup();
                    console.error('Failed to load combat music:', error);
                    this.combatMusic = null;
                    resolve({ success: false, error: error.message || 'Combat music loading failed' });
                };

                this.combatMusic.addEventListener('canplaythrough', onCanPlay);
                this.combatMusic.addEventListener('error', onError);

                // Start loading
                this.combatMusic.load();
            });

        } catch (error) {
            console.error('Failed to start combat music:', error);
            return { success: false, error: error.message };
        }
    }

    // Stop combat music and restore previous music
    async stopCombatMusic() {
        if (!this.inCombatMode) {
            console.log('Combat music not active');
            return { success: true };
        }

        try {
            // Stop combat music
            if (this.combatMusic) {
                this.combatMusic.pause();
                this.combatMusic.currentTime = 0;
                this.combatMusic = null;
                console.log('Combat music stopped');
            }

            this.inCombatMode = false;

            // Restore previous music if it was playing
            if (this.previousMusicPath && this.previousMusicCurrentTime >= 0) {
                console.log(`Restoring previous music from ${this.previousMusicCurrentTime.toFixed(2)}s: ${this.previousMusicPath}`);
                
                try {
                    // Verify the previous music file still exists
                    const fileCheck = await this.verifyAudioFile(this.previousMusicPath);
                    if (!fileCheck.success) {
                        throw new Error(`Previous music file not accessible: ${this.previousMusicPath}`);
                    }

                    // Create new audio element for the previous music
                    this.backgroundMusic = new Audio(this.previousMusicPath);
                    this.backgroundMusic.loop = true;
                    this.backgroundMusic.volume = this.backgroundMusicVolume * this.masterVolume;

                    // Set up event handlers to resume from the stored time
                    return new Promise((resolve) => {
                        const cleanup = () => {
                            this.backgroundMusic.removeEventListener('canplaythrough', onCanPlay);
                            this.backgroundMusic.removeEventListener('error', onError);
                        };

                        const onCanPlay = () => {
                            cleanup();
                            try {
                                // Seek to the stored time position
                                this.backgroundMusic.currentTime = this.previousMusicCurrentTime;
                                
                                // Start playback from the resumed position
                                this.backgroundMusic.play()
                                    .then(() => {
                                        console.log(`Previous music resumed successfully from ${this.previousMusicCurrentTime.toFixed(2)}s`);
                                        
                                        // Clear stored state after successful restoration
                                        this.previousMusic = null;
                                        this.previousMusicPath = null;
                                        this.previousMusicCurrentTime = 0;
                                        
                                        resolve({ success: true });
                                    })
                                    .catch((error) => {
                                        console.error('Failed to resume previous music playback:', error);
                                        this.backgroundMusic = null;
                                        resolve({ success: false, error: error.message });
                                    });
                            } catch (seekError) {
                                console.error('Failed to seek to previous music position:', seekError);
                                // If seeking fails, play from the beginning
                                this.backgroundMusic.currentTime = 0;
                                this.backgroundMusic.play()
                                    .then(() => {
                                        console.log('Previous music started from beginning (seek failed)');
                                        resolve({ success: true });
                                    })
                                    .catch((playError) => {
                                        console.error('Failed to play previous music:', playError);
                                        this.backgroundMusic = null;
                                        resolve({ success: false, error: playError.message });
                                    });
                            }
                        };

                        const onError = (error) => {
                            cleanup();
                            console.error('Failed to load previous music:', error);
                            this.backgroundMusic = null;
                            resolve({ success: false, error: error.message || 'Previous music loading failed' });
                        };

                        this.backgroundMusic.addEventListener('canplaythrough', onCanPlay);
                        this.backgroundMusic.addEventListener('error', onError);

                        // Start loading
                        this.backgroundMusic.load();
                    });

                } catch (restoreError) {
                    console.warn('Failed to restore previous music, falling back to floor-based music:', restoreError);
                    
                    // Fallback to floor-based music if restoration fails
                    const result = await this.playFloorBasedBackgroundMusic();
                    
                    // Clear stored state
                    this.previousMusic = null;
                    this.previousMusicPath = null;
                    this.previousMusicCurrentTime = 0;
                    
                    return result;
                }
            } else {
                // No previous music to restore, play floor-appropriate music
                console.log('No previous music to restore, starting floor-appropriate background music');
                
                const result = await this.playFloorBasedBackgroundMusic();
                
                // Clear stored state
                this.previousMusic = null;
                this.previousMusicPath = null;
                this.previousMusicCurrentTime = 0;
                
                return result;
            }

        } catch (error) {
            console.error('Failed to stop combat music and restore previous music:', error);
            
            // Clear stored state on error
            this.previousMusic = null;
            this.previousMusicPath = null;
            this.previousMusicCurrentTime = 0;
            
            return { success: false, error: error.message };
        }
    }

    // Check if combat music is playing
    isCombatMusicPlaying() {
        return this.combatMusic !== null && !this.combatMusic.paused;
    }

    // Play boss battle music for ultimate bosses (DARK_LORD, ANCIENT_EVIL)
    async playBossBattleMusic() {
        if (!this.enabled) {
            console.warn('Audio not enabled');
            return { success: false, error: 'Audio not enabled' };
        }

        if (this.inCombatMode) {
            console.log('Boss battle music already active or in combat mode');
            return { success: true };
        }

        try {
            const bossMusicPath = 'assets/audio/The End Of All Things.m4a';
            console.log('Starting boss battle music:', bossMusicPath);

            // Store current background music state for restoration (similar to combat music)
            if (this.backgroundMusic && !this.backgroundMusic.paused) {
                this.previousMusicCurrentTime = this.backgroundMusic.currentTime;
                
                let srcPath = this.backgroundMusic.src;
                if (srcPath.startsWith('file:///')) {
                    const url = new URL(srcPath);
                    this.previousMusicPath = url.pathname.split('/').pop();
                    this.previousMusicPath = 'assets/audio/' + this.previousMusicPath;
                } else if (srcPath.includes('assets/audio/')) {
                    const assetIndex = srcPath.indexOf('assets/audio/');
                    this.previousMusicPath = srcPath.substring(assetIndex);
                } else {
                    this.previousMusicPath = this.getFloorBasedMusicPath();
                }

                console.log(`Storing previous music state for boss battle - Path: ${this.previousMusicPath}, Time: ${this.previousMusicCurrentTime.toFixed(2)}s`);
            } else {
                this.previousMusicPath = null;
                this.previousMusicCurrentTime = 0;
            }

            // Verify boss music file exists
            const fileCheck = await this.verifyAudioFile(bossMusicPath);
            if (!fileCheck.success) {
                throw new Error(`Boss battle music file not found: ${bossMusicPath}`);
            }

            // Stop current background music
            this.stopBackgroundMusic();

            // Create and configure boss battle music
            this.combatMusic = new Audio(bossMusicPath);
            this.combatMusic.loop = true; // Boss music loops
            
            // Calculate and validate volume to prevent NaN errors
            const calculatedVolume = (this.combatMusicVolume || 0.8) * (this.masterVolume || 0.6);
            this.combatMusic.volume = Math.max(0, Math.min(1, isFinite(calculatedVolume) ? calculatedVolume : 0.5));
            this.inCombatMode = true;

            // Set up event handlers
            return new Promise((resolve) => {
                const cleanup = () => {
                    this.combatMusic.removeEventListener('canplaythrough', onCanPlay);
                    this.combatMusic.removeEventListener('error', onError);
                };

                const onCanPlay = () => {
                    cleanup();
                    this.combatMusic.play()
                        .then(() => {
                            console.log('Boss battle music started successfully');
                            resolve({ success: true });
                        })
                        .catch((error) => {
                            console.error('Failed to start boss battle music playback:', error);
                            this.combatMusic = null;
                            this.inCombatMode = false;
                            resolve({ success: false, error: error.message });
                        });
                };

                const onError = (error) => {
                    cleanup();
                    if (this.debug) {
                        console.error('Failed to load boss battle music:', error);
                    }
                    this.combatMusic = null;
                    this.inCombatMode = false;
                    resolve({ success: false, error: error.message || 'Boss battle music loading failed' });
                };

                this.combatMusic.addEventListener('canplaythrough', onCanPlay);
                this.combatMusic.addEventListener('error', onError);

                // Start loading
                this.combatMusic.load();
            });

        } catch (error) {
            if (this.debug) {
                console.error('Failed to load/play boss battle music:', error);
            }
            this.combatMusic = null;
            this.inCombatMode = false;
            return { success: false, error: error.message };
        }
    }

    // Play finale music for game completion
    async playFinaleMusic() {
        if (!this.enabled) {
            console.warn('Audio not enabled');
            return { success: false, error: 'Audio not enabled' };
        }

        try {
            const finaleMusicPath = 'assets/audio/Finale.m4a';
            console.log('Starting finale music:', finaleMusicPath);

            // Verify finale music file exists
            const fileCheck = await this.verifyAudioFile(finaleMusicPath);
            if (!fileCheck.success) {
                throw new Error(`Finale music file not found: ${finaleMusicPath}`);
            }

            // Stop any existing music
            this.stopBackgroundMusic();
            if (this.combatMusic) {
                this.combatMusic.pause();
                this.combatMusic = null;
            }
            this.inCombatMode = false;

            // Create and configure finale music
            this.backgroundMusic = new Audio(finaleMusicPath);
            this.backgroundMusic.loop = false; // Finale music plays once
            this.backgroundMusic.volume = this.backgroundMusicVolume * this.masterVolume;

            // Set up event handlers
            return new Promise((resolve) => {
                const cleanup = () => {
                    this.backgroundMusic.removeEventListener('canplaythrough', onCanPlay);
                    this.backgroundMusic.removeEventListener('error', onError);
                };

                const onCanPlay = () => {
                    cleanup();
                    this.backgroundMusic.play()
                        .then(() => {
                            console.log('Finale music started successfully');
                            resolve({ success: true });
                        })
                        .catch((error) => {
                            console.error('Failed to start finale music playback:', error);
                            this.backgroundMusic = null;
                            resolve({ success: false, error: error.message });
                        });
                };

                const onError = (error) => {
                    cleanup();
                    if (this.debug) {
                        console.error('Failed to load finale music:', error);
                    }
                    this.backgroundMusic = null;
                    resolve({ success: false, error: error.message || 'Finale music loading failed' });
                };

                this.backgroundMusic.addEventListener('canplaythrough', onCanPlay);
                this.backgroundMusic.addEventListener('error', onError);

                // Start loading
                this.backgroundMusic.load();
            });

        } catch (error) {
            if (this.debug) {
                console.error('Failed to load/play finale music:', error);
            }
            this.backgroundMusic = null;
            return { success: false, error: error.message };
        }
    }
}

// Create global instance
const soundManager = new SoundManager();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.soundManager = soundManager;
}
